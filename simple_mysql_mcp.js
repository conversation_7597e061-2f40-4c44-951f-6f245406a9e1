#!/usr/bin/env node

const mysql = require('mysql2/promise');

class SimpleMySQLMCP {
    constructor() {
        this.connection = null;
    }

    async connect() {
        try {
            this.connection = await mysql.createConnection({
                host: process.env.MYSQL_HOST || '127.0.0.1',
                port: parseInt(process.env.MYSQL_PORT) || 3306,
                user: process.env.MYSQL_USER || 'root',
                password: process.env.MYSQL_PASS || '123456',
                database: process.env.MYSQL_DB || 'app_service'
            });
            console.error('MySQL连接成功');
            return true;
        } catch (error) {
            console.error('MySQL连接失败:', error.message);
            return false;
        }
    }

    async handleRequest(request) {
        const { method, params } = request;
        
        switch (method) {
            case 'initialize':
                return {
                    protocolVersion: "2024-11-05",
                    capabilities: {
                        tools: {}
                    },
                    serverInfo: {
                        name: "simple-mysql-mcp",
                        version: "1.0.0"
                    }
                };
                
            case 'tools/list':
                return {
                    tools: [
                        {
                            name: "run_sql_query",
                            description: "Execute SQL query",
                            inputSchema: {
                                type: "object",
                                properties: {
                                    query: {
                                        type: "string",
                                        description: "SQL query to execute"
                                    }
                                },
                                required: ["query"]
                            }
                        }
                    ]
                };
                
            case 'tools/call':
                if (params.name === 'run_sql_query') {
                    return await this.executeQuery(params.arguments.query);
                }
                break;
        }
        
        throw new Error(`Unknown method: ${method}`);
    }

    async executeQuery(query) {
        try {
            if (!this.connection) {
                await this.connect();
            }
            
            const [rows] = await this.connection.execute(query);
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify(rows, null, 2)
                    }
                ]
            };
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error: ${error.message}`
                    }
                ],
                isError: true
            };
        }
    }
}

// MCP Server implementation
const server = new SimpleMySQLMCP();

process.stdin.on('data', async (data) => {
    try {
        const request = JSON.parse(data.toString());
        const response = await server.handleRequest(request);
        
        console.log(JSON.stringify({
            jsonrpc: "2.0",
            id: request.id,
            result: response
        }));
    } catch (error) {
        console.log(JSON.stringify({
            jsonrpc: "2.0",
            id: request.id || null,
            error: {
                code: -32000,
                message: error.message
            }
        }));
    }
});

// Initialize connection on startup
server.connect();
