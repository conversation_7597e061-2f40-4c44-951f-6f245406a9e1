{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "disabled": false, "autoApprove": ["sequentialthinking"]}, "mongodb-mcp-server": {"command": "npx", "args": ["-y", "mongodb-mcp-server"], "env": {"MDB_MCP_CONNECTION_STRING": "**************************************************************"}, "disabled": false, "autoApprove": []}, "yc-uimbg-API-文档": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--project-id=4013664"], "env": {"APIFOX_ACCESS_TOKEN": "APS-xQxpxSx0MAAbhym9VIMbFa038ToIgAZd"}, "disabled": false, "autoApprove": []}, "文潮go-API-文档": {"command": "npx", "args": ["-y", "apifox-mcp-server@latest", "--project-id=5498560"], "env": {"APIFOX_ACCESS_TOKEN": "APS-xQxpxSx0MAAbhym9VIMbFa038ToIgAZd"}, "disabled": false, "autoApprove": []}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/work/yqmt"], "env": {}, "disabled": false, "autoApprove": ["create_directory", "create_directory", "directory_tree"]}, "redis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-redis"], "env": {"REDIS_URL": "127.0.0.1@6379"}, "disabled": false, "autoApprove": []}, "Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"], "disabled": false, "autoApprove": ["get_figma_data"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "寸止": {"command": "寸止", "args": [], "disabled": true, "autoApprove": ["zhi", "ji"]}, "mysql-mcp-server": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "127.0.0.1", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASS": "123456", "MYSQL_DB": "app_service", "ALLOW_DDL_OPERATION": "true", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true"}, "disabled": false, "autoApprove": []}}}